/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/work/page";
exports.ids = ["app/work/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fwork%2Fpage&page=%2Fwork%2Fpage&appPaths=%2Fwork%2Fpage&pagePath=private-next-app-dir%2Fwork%2Fpage.tsx&appDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fwork%2Fpage&page=%2Fwork%2Fpage&appPaths=%2Fwork%2Fpage&pagePath=private-next-app-dir%2Fwork%2Fpage.tsx&appDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'work',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/work/page.tsx */ \"(rsc)/./src/app/work/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/work/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/work/page\",\n        pathname: \"/work\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fwork%2Fpage&page=%2Fwork%2Fpage&appPaths=%2Fwork%2Fpage&pagePath=private-next-app-dir%2Fwork%2Fpage.tsx&appDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Ccomponents%5Clayout%5CHeader.tsx&server=true!":
/*!************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Ccomponents%5Clayout%5CHeader.tsx&server=true! ***!
  \************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(ssr)/./src/components/layout/Header.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDU2F1ZCU1Q1Byb2plY3RzJTVDbmF2aGF1cyU1Q3NyYyU1Q2NvbXBvbmVudHMlNUNsYXlvdXQlNUNIZWFkZXIudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL25hdmhhdXMtd2Vic2l0ZS8/M2EyNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFNhdWRcXFxcUHJvamVjdHNcXFxcbmF2aGF1c1xcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxsYXlvdXRcXFxcSGVhZGVyLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Ccomponents%5Clayout%5CHeader.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const navItems = [\n        {\n            href: \"/\",\n            label: \"Home\"\n        },\n        {\n            href: \"/about\",\n            label: \"About\"\n        },\n        {\n            href: \"/work\",\n            label: \"Work\"\n        },\n        {\n            href: \"/contact\",\n            label: \"Contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"w-full py-6 px-6 md:px-12 lg:px-24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"text-2xl font-bold uppercase tracking-wide\",\n                        children: \"Navhaus\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center space-x-8\",\n                        children: [\n                            navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `font-medium uppercase tracking-wide transition-colors duration-200 ${pathname === item.href ? \"text-bauhaus-red\" : \"text-bauhaus-black hover:text-bauhaus-red\"}`,\n                                    children: item.label\n                                }, item.href, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/contact\",\n                                className: \"btn-primary ml-8\",\n                                children: \"Start Project\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"md:hidden text-bauhaus-black\",\n                        onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M4 6h16M4 12h16M4 18h16\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden mt-6 py-6 border-t border-bauhaus-black\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col space-y-4\",\n                    children: [\n                        navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                className: `font-medium uppercase tracking-wide transition-colors duration-200 ${pathname === item.href ? \"text-bauhaus-red\" : \"text-bauhaus-black hover:text-bauhaus-red\"}`,\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: item.label\n                            }, item.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/contact\",\n                            className: \"btn-primary inline-block mt-4\",\n                            onClick: ()=>setIsMenuOpen(false),\n                            children: \"Start Project\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2e232213ba7d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmF2aGF1cy13ZWJzaXRlLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9lOTdlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMmUyMzIyMTNiYTdkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"Navhaus - What matters, made real\",\n    description: \"Navhaus is a design and development studio that builds bold, efficient, and meaningful digital experiences — nothing more, nothing less.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7c0JBQU1IOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmF2aGF1cy13ZWJzaXRlLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnTmF2aGF1cyAtIFdoYXQgbWF0dGVycywgbWFkZSByZWFsJyxcbiAgZGVzY3JpcHRpb246ICdOYXZoYXVzIGlzIGEgZGVzaWduIGFuZCBkZXZlbG9wbWVudCBzdHVkaW8gdGhhdCBidWlsZHMgYm9sZCwgZWZmaWNpZW50LCBhbmQgbWVhbmluZ2Z1bCBkaWdpdGFsIGV4cGVyaWVuY2VzIOKAlCBub3RoaW5nIG1vcmUsIG5vdGhpbmcgbGVzcy4nLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5PntjaGlsZHJlbn08L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/work/page.tsx":
/*!*******************************!*\
  !*** ./src/app/work/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Work)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_PageWrapper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/PageWrapper */ \"(rsc)/./src/components/layout/PageWrapper.tsx\");\n/* harmony import */ var _components_shapes_Circle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/shapes/Circle */ \"(rsc)/./src/components/shapes/Circle.tsx\");\n/* harmony import */ var _components_shapes_Rectangle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/shapes/Rectangle */ \"(rsc)/./src/components/shapes/Rectangle.tsx\");\n/* harmony import */ var _components_shapes_Triangle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/shapes/Triangle */ \"(rsc)/./src/components/shapes/Triangle.tsx\");\n/* harmony import */ var _components_shapes_HalfCircle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/shapes/HalfCircle */ \"(rsc)/./src/components/shapes/HalfCircle.tsx\");\n\n\n\n\n\n\nfunction Work() {\n    const projects = [\n        {\n            name: \"X Platform Redesign\",\n            summary: \"Speed-focused UI for a growing SaaS\",\n            timeline: \"3 weeks\",\n            outcome: \"2x conversions\",\n            tech: \"React + Tailwind\",\n            color: \"red\",\n            shapes: [\n                {\n                    type: \"circle\",\n                    size: \"lg\",\n                    color: \"white\"\n                },\n                {\n                    type: \"rectangle\",\n                    width: \"md\",\n                    height: \"sm\",\n                    color: \"black\"\n                }\n            ]\n        },\n        {\n            name: \"Fintech Mobile App\",\n            summary: \"Clean interface for complex financial data\",\n            timeline: \"6 weeks\",\n            outcome: \"40% faster tasks\",\n            tech: \"React Native + TypeScript\",\n            color: \"blue\",\n            shapes: [\n                {\n                    type: \"triangle\",\n                    size: \"lg\",\n                    color: \"white\",\n                    direction: \"up\"\n                },\n                {\n                    type: \"circle\",\n                    size: \"md\",\n                    color: \"yellow\"\n                }\n            ]\n        },\n        {\n            name: \"E-commerce Rebuild\",\n            summary: \"Modern storefront with zero downtime migration\",\n            timeline: \"8 weeks\",\n            outcome: \"3x page speed\",\n            tech: \"Next.js + Shopify\",\n            color: \"yellow\",\n            shapes: [\n                {\n                    type: \"halfcircle\",\n                    size: \"lg\",\n                    color: \"black\",\n                    direction: \"top\"\n                },\n                {\n                    type: \"rectangle\",\n                    width: \"sm\",\n                    height: \"lg\",\n                    color: \"red\"\n                }\n            ]\n        },\n        {\n            name: \"Healthcare Dashboard\",\n            summary: \"Data visualization for medical professionals\",\n            timeline: \"5 weeks\",\n            outcome: \"50% time saved\",\n            tech: \"Vue.js + D3.js\",\n            color: \"red\",\n            shapes: [\n                {\n                    type: \"circle\",\n                    size: \"xl\",\n                    color: \"blue\"\n                },\n                {\n                    type: \"triangle\",\n                    size: \"md\",\n                    color: \"white\",\n                    direction: \"down\"\n                }\n            ]\n        },\n        {\n            name: \"Startup Landing Page\",\n            summary: \"High-converting page for Series A fundraising\",\n            timeline: \"2 weeks\",\n            outcome: \"$2M raised\",\n            tech: \"Gatsby + Netlify\",\n            color: \"blue\",\n            shapes: [\n                {\n                    type: \"rectangle\",\n                    width: \"lg\",\n                    height: \"lg\",\n                    color: \"yellow\"\n                },\n                {\n                    type: \"circle\",\n                    size: \"sm\",\n                    color: \"black\"\n                }\n            ]\n        },\n        {\n            name: \"SaaS Product Redesign\",\n            summary: \"Complete UX overhaul for B2B platform\",\n            timeline: \"12 weeks\",\n            outcome: \"60% less support tickets\",\n            tech: \"React + Node.js\",\n            color: \"yellow\",\n            shapes: [\n                {\n                    type: \"halfcircle\",\n                    size: \"xl\",\n                    color: \"red\",\n                    direction: \"bottom\"\n                },\n                {\n                    type: \"triangle\",\n                    size: \"sm\",\n                    color: \"blue\",\n                    direction: \"right\"\n                }\n            ]\n        }\n    ];\n    const renderShape = (shape, index)=>{\n        const key = `shape-${index}`;\n        const className = index === 0 ? \"absolute top-4 left-4\" : \"absolute bottom-4 right-4\";\n        switch(shape.type){\n            case \"circle\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_Circle__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    size: shape.size,\n                    color: shape.color,\n                    className: className\n                }, key, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 16\n                }, this);\n            case \"rectangle\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_Rectangle__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    width: shape.width,\n                    height: shape.height,\n                    color: shape.color,\n                    className: className\n                }, key, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 16\n                }, this);\n            case \"triangle\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_Triangle__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    size: shape.size,\n                    color: shape.color,\n                    direction: shape.direction,\n                    className: className\n                }, key, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 16\n                }, this);\n            case \"halfcircle\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shapes_HalfCircle__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    size: shape.size,\n                    color: shape.color,\n                    direction: shape.direction,\n                    className: className\n                }, key, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_PageWrapper__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"px-6 md:px-12 lg:px-24 py-16 md:py-24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-display font-bold mb-8\",\n                        children: \"Projects That Speak for Themselves\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"px-6 md:px-12 lg:px-24 pb-16 md:pb-24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `relative p-8 border-2 border-bauhaus-black bg-bauhaus-${project.color} min-h-[300px] flex flex-col justify-between overflow-hidden`,\n                                children: [\n                                    project.shapes.map((shape, shapeIndex)=>renderShape(shape, shapeIndex)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-heading font-bold mb-3 text-bauhaus-black\",\n                                                children: project.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-body mb-6 text-bauhaus-black\",\n                                                children: project.summary\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10 space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-bauhaus-black\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"opacity-75\",\n                                                        children: \"Timeline:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" \",\n                                                    project.timeline\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-bauhaus-black\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"opacity-75\",\n                                                        children: \"Outcome:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" \",\n                                                    project.outcome\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-bauhaus-black\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"opacity-75\",\n                                                        children: \"Tech:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" \",\n                                                    project.tech\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\app\\\\work\\\\page.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/work/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"w-full py-12 px-6 md:px-12 lg:px-24 border-t border-bauhaus-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row justify-between items-start md:items-center space-y-6 md:space-y-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xl font-bold uppercase tracking-wide mb-2\",\n                                children: \"Navhaus\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 7,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"What matters, made real.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 10,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 6,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium mb-1\",\n                                children: \"Ready to build something?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"mailto:<EMAIL>\",\n                                className: \"text-sm text-bauhaus-red hover:underline\",\n                                children: \"<EMAIL>\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 4,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 pt-6 border-t border-gray-200 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [\n                        \"\\xa9 \",\n                        new Date().getFullYear(),\n                        \" Navhaus. All rights reserved.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Projects\navhaus\src\components\layout\Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/layout/PageWrapper.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/PageWrapper.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PageWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Header */ \"(rsc)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n\n\n\nfunction PageWrapper({ children, className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\PageWrapper.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: `flex-1 ${className}`,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\PageWrapper.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\PageWrapper.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\layout\\\\PageWrapper.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvUGFnZVdyYXBwZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE2QjtBQUNBO0FBT2QsU0FBU0UsWUFBWSxFQUFFQyxRQUFRLEVBQUVDLFlBQVksRUFBRSxFQUFvQjtJQUNoRixxQkFDRSw4REFBQ0M7UUFBSUQsV0FBVTs7MEJBQ2IsOERBQUNKLCtDQUFNQTs7Ozs7MEJBQ1AsOERBQUNNO2dCQUFLRixXQUFXLENBQUMsT0FBTyxFQUFFQSxVQUFVLENBQUM7MEJBQ25DRDs7Ozs7OzBCQUVILDhEQUFDRiwrQ0FBTUE7Ozs7Ozs7Ozs7O0FBR2IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uYXZoYXVzLXdlYnNpdGUvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvUGFnZVdyYXBwZXIudHN4PzE2N2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEhlYWRlciBmcm9tICcuL0hlYWRlcidcbmltcG9ydCBGb290ZXIgZnJvbSAnLi9Gb290ZXInXG5cbmludGVyZmFjZSBQYWdlV3JhcHBlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFnZVdyYXBwZXIoeyBjaGlsZHJlbiwgY2xhc3NOYW1lID0gJycgfTogUGFnZVdyYXBwZXJQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggZmxleC1jb2xcIj5cbiAgICAgIDxIZWFkZXIgLz5cbiAgICAgIDxtYWluIGNsYXNzTmFtZT17YGZsZXgtMSAke2NsYXNzTmFtZX1gfT5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9tYWluPlxuICAgICAgPEZvb3RlciAvPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiSGVhZGVyIiwiRm9vdGVyIiwiUGFnZVdyYXBwZXIiLCJjaGlsZHJlbiIsImNsYXNzTmFtZSIsImRpdiIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/PageWrapper.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/shapes/Circle.tsx":
/*!******************************************!*\
  !*** ./src/components/shapes/Circle.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Circle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst sizeClasses = {\n    sm: \"w-8 h-8\",\n    md: \"w-16 h-16\",\n    lg: \"w-24 h-24\",\n    xl: \"w-32 h-32\"\n};\nconst colorClasses = {\n    red: \"bg-bauhaus-red\",\n    yellow: \"bg-bauhaus-yellow\",\n    blue: \"bg-bauhaus-blue\",\n    black: \"bg-bauhaus-black\",\n    white: \"bg-bauhaus-white border border-bauhaus-black\"\n};\nfunction Circle({ size = \"md\", color = \"red\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-full ${sizeClasses[size]} ${colorClasses[color]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Circle.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9zaGFwZXMvQ2lyY2xlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBTUEsTUFBTUEsY0FBYztJQUNsQkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtBQUNOO0FBRUEsTUFBTUMsZUFBZTtJQUNuQkMsS0FBSztJQUNMQyxRQUFRO0lBQ1JDLE1BQU07SUFDTkMsT0FBTztJQUNQQyxPQUFPO0FBQ1Q7QUFFZSxTQUFTQyxPQUFPLEVBQzdCQyxPQUFPLElBQUksRUFDWEMsUUFBUSxLQUFLLEVBQ2JDLFlBQVksRUFBRSxFQUNGO0lBQ1oscUJBQ0UsOERBQUNDO1FBQ0NELFdBQVcsQ0FBQyxhQUFhLEVBQUVkLFdBQVcsQ0FBQ1ksS0FBSyxDQUFDLENBQUMsRUFBRVAsWUFBWSxDQUFDUSxNQUFNLENBQUMsQ0FBQyxFQUFFQyxVQUFVLENBQUM7Ozs7OztBQUd4RiIsInNvdXJjZXMiOlsid2VicGFjazovL25hdmhhdXMtd2Vic2l0ZS8uL3NyYy9jb21wb25lbnRzL3NoYXBlcy9DaXJjbGUudHN4P2YyMjUiXSwic291cmNlc0NvbnRlbnQiOlsiaW50ZXJmYWNlIENpcmNsZVByb3BzIHtcbiAgc2l6ZT86ICdzbScgfCAnbWQnIHwgJ2xnJyB8ICd4bCdcbiAgY29sb3I/OiAncmVkJyB8ICd5ZWxsb3cnIHwgJ2JsdWUnIHwgJ2JsYWNrJyB8ICd3aGl0ZSdcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59XG5cbmNvbnN0IHNpemVDbGFzc2VzID0ge1xuICBzbTogJ3ctOCBoLTgnLFxuICBtZDogJ3ctMTYgaC0xNicsIFxuICBsZzogJ3ctMjQgaC0yNCcsXG4gIHhsOiAndy0zMiBoLTMyJ1xufVxuXG5jb25zdCBjb2xvckNsYXNzZXMgPSB7XG4gIHJlZDogJ2JnLWJhdWhhdXMtcmVkJyxcbiAgeWVsbG93OiAnYmctYmF1aGF1cy15ZWxsb3cnLFxuICBibHVlOiAnYmctYmF1aGF1cy1ibHVlJywgXG4gIGJsYWNrOiAnYmctYmF1aGF1cy1ibGFjaycsXG4gIHdoaXRlOiAnYmctYmF1aGF1cy13aGl0ZSBib3JkZXIgYm9yZGVyLWJhdWhhdXMtYmxhY2snXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENpcmNsZSh7IFxuICBzaXplID0gJ21kJywgXG4gIGNvbG9yID0gJ3JlZCcsIFxuICBjbGFzc05hbWUgPSAnJyBcbn06IENpcmNsZVByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBcbiAgICAgIGNsYXNzTmFtZT17YHJvdW5kZWQtZnVsbCAke3NpemVDbGFzc2VzW3NpemVdfSAke2NvbG9yQ2xhc3Nlc1tjb2xvcl19ICR7Y2xhc3NOYW1lfWB9XG4gICAgLz5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInNpemVDbGFzc2VzIiwic20iLCJtZCIsImxnIiwieGwiLCJjb2xvckNsYXNzZXMiLCJyZWQiLCJ5ZWxsb3ciLCJibHVlIiwiYmxhY2siLCJ3aGl0ZSIsIkNpcmNsZSIsInNpemUiLCJjb2xvciIsImNsYXNzTmFtZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/shapes/Circle.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/shapes/HalfCircle.tsx":
/*!**********************************************!*\
  !*** ./src/components/shapes/HalfCircle.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HalfCircle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst sizeClasses = {\n    sm: \"w-8 h-4\",\n    md: \"w-16 h-8\",\n    lg: \"w-24 h-12\",\n    xl: \"w-32 h-16\"\n};\nconst colorClasses = {\n    red: \"bg-bauhaus-red\",\n    yellow: \"bg-bauhaus-yellow\",\n    blue: \"bg-bauhaus-blue\",\n    black: \"bg-bauhaus-black\",\n    white: \"bg-bauhaus-white border border-bauhaus-black\"\n};\nconst directionClasses = {\n    top: \"rounded-t-full\",\n    bottom: \"rounded-b-full\",\n    left: \"rounded-l-full\",\n    right: \"rounded-r-full\"\n};\nconst getRotationClasses = (direction)=>{\n    switch(direction){\n        case \"left\":\n            return \"w-4 h-8 md:w-8 md:h-16 lg:w-12 lg:h-24 xl:w-16 xl:h-32\";\n        case \"right\":\n            return \"w-4 h-8 md:w-8 md:h-16 lg:w-12 lg:h-24 xl:w-16 xl:h-32\";\n        default:\n            return \"\";\n    }\n};\nfunction HalfCircle({ size = \"md\", color = \"red\", direction = \"top\", className = \"\" }) {\n    const rotationClasses = getRotationClasses(direction);\n    const finalSizeClasses = rotationClasses || sizeClasses[size];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${finalSizeClasses} ${colorClasses[color]} ${directionClasses[direction]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\HalfCircle.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/shapes/HalfCircle.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/shapes/Rectangle.tsx":
/*!*********************************************!*\
  !*** ./src/components/shapes/Rectangle.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Rectangle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst widthClasses = {\n    sm: \"w-12\",\n    md: \"w-24\",\n    lg: \"w-32\",\n    xl: \"w-48\"\n};\nconst heightClasses = {\n    sm: \"h-8\",\n    md: \"h-16\",\n    lg: \"h-24\",\n    xl: \"h-32\"\n};\nconst colorClasses = {\n    red: \"bg-bauhaus-red\",\n    yellow: \"bg-bauhaus-yellow\",\n    blue: \"bg-bauhaus-blue\",\n    black: \"bg-bauhaus-black\",\n    white: \"bg-bauhaus-white border border-bauhaus-black\"\n};\nfunction Rectangle({ width = \"md\", height = \"md\", color = \"blue\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${widthClasses[width]} ${heightClasses[height]} ${colorClasses[color]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Rectangle.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/shapes/Rectangle.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/shapes/Triangle.tsx":
/*!********************************************!*\
  !*** ./src/components/shapes/Triangle.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Triangle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst sizeClasses = {\n    sm: \"w-0 h-0 border-l-[16px] border-r-[16px] border-b-[28px]\",\n    md: \"w-0 h-0 border-l-[24px] border-r-[24px] border-b-[42px]\",\n    lg: \"w-0 h-0 border-l-[32px] border-r-[32px] border-b-[56px]\",\n    xl: \"w-0 h-0 border-l-[48px] border-r-[48px] border-b-[84px]\"\n};\nconst getTriangleClasses = (size, color, direction)=>{\n    const colorMap = {\n        red: \"#FF0000\",\n        yellow: \"#FFFF00\",\n        blue: \"#0000FF\",\n        black: \"#000000\",\n        white: \"#FFFFFF\"\n    };\n    const baseSize = sizeClasses[size];\n    const triangleColor = colorMap[color];\n    switch(direction){\n        case \"up\":\n            return `${baseSize} border-l-transparent border-r-transparent`;\n        case \"down\":\n            return baseSize.replace(\"border-b-\", \"border-t-\") + \" border-l-transparent border-r-transparent\";\n        case \"left\":\n            return baseSize.replace(\"border-l-\", \"border-r-\").replace(\"border-r-\", \"border-t-\").replace(\"border-b-\", \"border-l-\") + \" border-t-transparent border-b-transparent\";\n        case \"right\":\n            return baseSize.replace(\"border-r-\", \"border-l-\").replace(\"border-l-\", \"border-t-\").replace(\"border-b-\", \"border-r-\") + \" border-t-transparent border-b-transparent\";\n        default:\n            return `${baseSize} border-l-transparent border-r-transparent`;\n    }\n};\nfunction Triangle({ size = \"md\", color = \"yellow\", direction = \"up\", className = \"\" }) {\n    const triangleClasses = getTriangleClasses(size, color, direction);\n    const style = {\n        borderBottomColor: direction === \"up\" ? color === \"red\" ? \"#FF0000\" : color === \"yellow\" ? \"#FFFF00\" : color === \"blue\" ? \"#0000FF\" : color === \"black\" ? \"#000000\" : \"#FFFFFF\" : \"transparent\",\n        borderTopColor: direction === \"down\" ? color === \"red\" ? \"#FF0000\" : color === \"yellow\" ? \"#FFFF00\" : color === \"blue\" ? \"#0000FF\" : color === \"black\" ? \"#000000\" : \"#FFFFFF\" : \"transparent\",\n        borderLeftColor: direction === \"right\" ? color === \"red\" ? \"#FF0000\" : color === \"yellow\" ? \"#FFFF00\" : color === \"blue\" ? \"#0000FF\" : color === \"black\" ? \"#000000\" : \"#FFFFFF\" : \"transparent\",\n        borderRightColor: direction === \"left\" ? color === \"red\" ? \"#FF0000\" : color === \"yellow\" ? \"#FFFF00\" : color === \"blue\" ? \"#0000FF\" : color === \"black\" ? \"#000000\" : \"#FFFFFF\" : \"transparent\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${triangleClasses} ${className}`,\n        style: style\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Triangle.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/shapes/Triangle.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fwork%2Fpage&page=%2Fwork%2Fpage&appPaths=%2Fwork%2Fpage&pagePath=private-next-app-dir%2Fwork%2Fpage.tsx&appDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSaud%5CProjects%5Cnavhaus&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();