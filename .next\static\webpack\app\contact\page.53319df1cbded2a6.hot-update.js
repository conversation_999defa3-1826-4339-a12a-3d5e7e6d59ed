"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./src/components/shapes/Triangle.tsx":
/*!********************************************!*\
  !*** ./src/components/shapes/Triangle.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Triangle; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n\nconst sizeClasses = {\n    sm: \"w-0 h-0 border-l-[16px] border-r-[16px] border-b-[28px]\",\n    md: \"w-0 h-0 border-l-[24px] border-r-[24px] border-b-[42px]\",\n    lg: \"w-0 h-0 border-l-[32px] border-r-[32px] border-b-[56px]\",\n    xl: \"w-0 h-0 border-l-[48px] border-r-[48px] border-b-[84px]\"\n};\nconst getTriangleClasses = (size, color, direction)=>{\n    const colorMap = {\n        red: \"#e94436\",\n        yellow: \"#ffc527\",\n        blue: \"#434897\",\n        black: \"#000000\",\n        white: \"#ffffff\"\n    };\n    const baseSize = sizeClasses[size];\n    const triangleColor = colorMap[color];\n    switch(direction){\n        case \"up\":\n            return \"\".concat(baseSize, \" border-l-transparent border-r-transparent\");\n        case \"down\":\n            return baseSize.replace(\"border-b-\", \"border-t-\") + \" border-l-transparent border-r-transparent\";\n        case \"left\":\n            return baseSize.replace(\"border-l-\", \"border-r-\").replace(\"border-r-\", \"border-t-\").replace(\"border-b-\", \"border-l-\") + \" border-t-transparent border-b-transparent\";\n        case \"right\":\n            return baseSize.replace(\"border-r-\", \"border-l-\").replace(\"border-l-\", \"border-t-\").replace(\"border-b-\", \"border-r-\") + \" border-t-transparent border-b-transparent\";\n        default:\n            return \"\".concat(baseSize, \" border-l-transparent border-r-transparent\");\n    }\n};\nfunction Triangle(param) {\n    let { size = \"md\", color = \"yellow\", direction = \"up\", className = \"\" } = param;\n    const triangleClasses = getTriangleClasses(size, color, direction);\n    const style = {\n        borderBottomColor: direction === \"up\" ? color === \"red\" ? \"#FF0000\" : color === \"yellow\" ? \"#FFFF00\" : color === \"blue\" ? \"#0000FF\" : color === \"black\" ? \"#000000\" : \"#FFFFFF\" : \"transparent\",\n        borderTopColor: direction === \"down\" ? color === \"red\" ? \"#FF0000\" : color === \"yellow\" ? \"#FFFF00\" : color === \"blue\" ? \"#0000FF\" : color === \"black\" ? \"#000000\" : \"#FFFFFF\" : \"transparent\",\n        borderLeftColor: direction === \"right\" ? color === \"red\" ? \"#FF0000\" : color === \"yellow\" ? \"#FFFF00\" : color === \"blue\" ? \"#0000FF\" : color === \"black\" ? \"#000000\" : \"#FFFFFF\" : \"transparent\",\n        borderRightColor: direction === \"left\" ? color === \"red\" ? \"#FF0000\" : color === \"yellow\" ? \"#FFFF00\" : color === \"blue\" ? \"#0000FF\" : color === \"black\" ? \"#000000\" : \"#FFFFFF\" : \"transparent\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(triangleClasses, \" \").concat(className),\n        style: style\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Triangle.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_c = Triangle;\nvar _c;\n$RefreshReg$(_c, \"Triangle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3NoYXBlcy9UcmlhbmdsZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBT0EsTUFBTUEsY0FBYztJQUNsQkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtBQUNOO0FBRUEsTUFBTUMscUJBQXFCLENBQUNDLE1BQWNDLE9BQWVDO0lBQ3ZELE1BQU1DLFdBQVc7UUFDZkMsS0FBSztRQUNMQyxRQUFRO1FBQ1JDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxPQUFPO0lBQ1Q7SUFFQSxNQUFNQyxXQUFXZixXQUFXLENBQUNNLEtBQWlDO0lBQzlELE1BQU1VLGdCQUFnQlAsUUFBUSxDQUFDRixNQUErQjtJQUU5RCxPQUFRQztRQUNOLEtBQUs7WUFDSCxPQUFPLEdBQVksT0FBVE8sVUFBUztRQUNyQixLQUFLO1lBQ0gsT0FBT0EsU0FBU0UsT0FBTyxDQUFDLGFBQWEsZUFBZTtRQUN0RCxLQUFLO1lBQ0gsT0FBT0YsU0FBU0UsT0FBTyxDQUFDLGFBQWEsYUFBYUEsT0FBTyxDQUFDLGFBQWEsYUFBYUEsT0FBTyxDQUFDLGFBQWEsZUFBZTtRQUMxSCxLQUFLO1lBQ0gsT0FBT0YsU0FBU0UsT0FBTyxDQUFDLGFBQWEsYUFBYUEsT0FBTyxDQUFDLGFBQWEsYUFBYUEsT0FBTyxDQUFDLGFBQWEsZUFBZTtRQUMxSDtZQUNFLE9BQU8sR0FBWSxPQUFURixVQUFTO0lBQ3ZCO0FBQ0Y7QUFFZSxTQUFTRyxTQUFTLEtBS2pCO1FBTGlCLEVBQy9CWixPQUFPLElBQUksRUFDWEMsUUFBUSxRQUFRLEVBQ2hCQyxZQUFZLElBQUksRUFDaEJXLFlBQVksRUFBRSxFQUNBLEdBTGlCO0lBTS9CLE1BQU1DLGtCQUFrQmYsbUJBQW1CQyxNQUFNQyxPQUFPQztJQUV4RCxNQUFNYSxRQUFRO1FBQ1pDLG1CQUFtQmQsY0FBYyxPQUFRRCxVQUFVLFFBQVEsWUFBWUEsVUFBVSxXQUFXLFlBQVlBLFVBQVUsU0FBUyxZQUFZQSxVQUFVLFVBQVUsWUFBWSxZQUFhO1FBQ3BMZ0IsZ0JBQWdCZixjQUFjLFNBQVVELFVBQVUsUUFBUSxZQUFZQSxVQUFVLFdBQVcsWUFBWUEsVUFBVSxTQUFTLFlBQVlBLFVBQVUsVUFBVSxZQUFZLFlBQWE7UUFDbkxpQixpQkFBaUJoQixjQUFjLFVBQVdELFVBQVUsUUFBUSxZQUFZQSxVQUFVLFdBQVcsWUFBWUEsVUFBVSxTQUFTLFlBQVlBLFVBQVUsVUFBVSxZQUFZLFlBQWE7UUFDckxrQixrQkFBa0JqQixjQUFjLFNBQVVELFVBQVUsUUFBUSxZQUFZQSxVQUFVLFdBQVcsWUFBWUEsVUFBVSxTQUFTLFlBQVlBLFVBQVUsVUFBVSxZQUFZLFlBQWE7SUFDdkw7SUFFQSxxQkFDRSw4REFBQ21CO1FBQ0NQLFdBQVcsR0FBc0JBLE9BQW5CQyxpQkFBZ0IsS0FBYSxPQUFWRDtRQUNqQ0UsT0FBT0E7Ozs7OztBQUdiO0tBckJ3QkgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvc2hhcGVzL1RyaWFuZ2xlLnRzeD84MDM3Il0sInNvdXJjZXNDb250ZW50IjpbImludGVyZmFjZSBUcmlhbmdsZVByb3BzIHtcbiAgc2l6ZT86ICdzbScgfCAnbWQnIHwgJ2xnJyB8ICd4bCdcbiAgY29sb3I/OiAncmVkJyB8ICd5ZWxsb3cnIHwgJ2JsdWUnIHwgJ2JsYWNrJyB8ICd3aGl0ZSdcbiAgZGlyZWN0aW9uPzogJ3VwJyB8ICdkb3duJyB8ICdsZWZ0JyB8ICdyaWdodCdcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59XG5cbmNvbnN0IHNpemVDbGFzc2VzID0ge1xuICBzbTogJ3ctMCBoLTAgYm9yZGVyLWwtWzE2cHhdIGJvcmRlci1yLVsxNnB4XSBib3JkZXItYi1bMjhweF0nLFxuICBtZDogJ3ctMCBoLTAgYm9yZGVyLWwtWzI0cHhdIGJvcmRlci1yLVsyNHB4XSBib3JkZXItYi1bNDJweF0nLFxuICBsZzogJ3ctMCBoLTAgYm9yZGVyLWwtWzMycHhdIGJvcmRlci1yLVszMnB4XSBib3JkZXItYi1bNTZweF0nLFxuICB4bDogJ3ctMCBoLTAgYm9yZGVyLWwtWzQ4cHhdIGJvcmRlci1yLVs0OHB4XSBib3JkZXItYi1bODRweF0nXG59XG5cbmNvbnN0IGdldFRyaWFuZ2xlQ2xhc3NlcyA9IChzaXplOiBzdHJpbmcsIGNvbG9yOiBzdHJpbmcsIGRpcmVjdGlvbjogc3RyaW5nKSA9PiB7XG4gIGNvbnN0IGNvbG9yTWFwID0ge1xuICAgIHJlZDogJyNlOTQ0MzYnLFxuICAgIHllbGxvdzogJyNmZmM1MjcnLFxuICAgIGJsdWU6ICcjNDM0ODk3JyxcbiAgICBibGFjazogJyMwMDAwMDAnLFxuICAgIHdoaXRlOiAnI2ZmZmZmZidcbiAgfVxuICBcbiAgY29uc3QgYmFzZVNpemUgPSBzaXplQ2xhc3Nlc1tzaXplIGFzIGtleW9mIHR5cGVvZiBzaXplQ2xhc3Nlc11cbiAgY29uc3QgdHJpYW5nbGVDb2xvciA9IGNvbG9yTWFwW2NvbG9yIGFzIGtleW9mIHR5cGVvZiBjb2xvck1hcF1cbiAgXG4gIHN3aXRjaCAoZGlyZWN0aW9uKSB7XG4gICAgY2FzZSAndXAnOlxuICAgICAgcmV0dXJuIGAke2Jhc2VTaXplfSBib3JkZXItbC10cmFuc3BhcmVudCBib3JkZXItci10cmFuc3BhcmVudGBcbiAgICBjYXNlICdkb3duJzpcbiAgICAgIHJldHVybiBiYXNlU2l6ZS5yZXBsYWNlKCdib3JkZXItYi0nLCAnYm9yZGVyLXQtJykgKyAnIGJvcmRlci1sLXRyYW5zcGFyZW50IGJvcmRlci1yLXRyYW5zcGFyZW50J1xuICAgIGNhc2UgJ2xlZnQnOlxuICAgICAgcmV0dXJuIGJhc2VTaXplLnJlcGxhY2UoJ2JvcmRlci1sLScsICdib3JkZXItci0nKS5yZXBsYWNlKCdib3JkZXItci0nLCAnYm9yZGVyLXQtJykucmVwbGFjZSgnYm9yZGVyLWItJywgJ2JvcmRlci1sLScpICsgJyBib3JkZXItdC10cmFuc3BhcmVudCBib3JkZXItYi10cmFuc3BhcmVudCdcbiAgICBjYXNlICdyaWdodCc6XG4gICAgICByZXR1cm4gYmFzZVNpemUucmVwbGFjZSgnYm9yZGVyLXItJywgJ2JvcmRlci1sLScpLnJlcGxhY2UoJ2JvcmRlci1sLScsICdib3JkZXItdC0nKS5yZXBsYWNlKCdib3JkZXItYi0nLCAnYm9yZGVyLXItJykgKyAnIGJvcmRlci10LXRyYW5zcGFyZW50IGJvcmRlci1iLXRyYW5zcGFyZW50J1xuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gYCR7YmFzZVNpemV9IGJvcmRlci1sLXRyYW5zcGFyZW50IGJvcmRlci1yLXRyYW5zcGFyZW50YFxuICB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFRyaWFuZ2xlKHsgXG4gIHNpemUgPSAnbWQnLFxuICBjb2xvciA9ICd5ZWxsb3cnLCBcbiAgZGlyZWN0aW9uID0gJ3VwJyxcbiAgY2xhc3NOYW1lID0gJydcbn06IFRyaWFuZ2xlUHJvcHMpIHtcbiAgY29uc3QgdHJpYW5nbGVDbGFzc2VzID0gZ2V0VHJpYW5nbGVDbGFzc2VzKHNpemUsIGNvbG9yLCBkaXJlY3Rpb24pXG4gIFxuICBjb25zdCBzdHlsZSA9IHtcbiAgICBib3JkZXJCb3R0b21Db2xvcjogZGlyZWN0aW9uID09PSAndXAnID8gKGNvbG9yID09PSAncmVkJyA/ICcjRkYwMDAwJyA6IGNvbG9yID09PSAneWVsbG93JyA/ICcjRkZGRjAwJyA6IGNvbG9yID09PSAnYmx1ZScgPyAnIzAwMDBGRicgOiBjb2xvciA9PT0gJ2JsYWNrJyA/ICcjMDAwMDAwJyA6ICcjRkZGRkZGJykgOiAndHJhbnNwYXJlbnQnLFxuICAgIGJvcmRlclRvcENvbG9yOiBkaXJlY3Rpb24gPT09ICdkb3duJyA/IChjb2xvciA9PT0gJ3JlZCcgPyAnI0ZGMDAwMCcgOiBjb2xvciA9PT0gJ3llbGxvdycgPyAnI0ZGRkYwMCcgOiBjb2xvciA9PT0gJ2JsdWUnID8gJyMwMDAwRkYnIDogY29sb3IgPT09ICdibGFjaycgPyAnIzAwMDAwMCcgOiAnI0ZGRkZGRicpIDogJ3RyYW5zcGFyZW50JyxcbiAgICBib3JkZXJMZWZ0Q29sb3I6IGRpcmVjdGlvbiA9PT0gJ3JpZ2h0JyA/IChjb2xvciA9PT0gJ3JlZCcgPyAnI0ZGMDAwMCcgOiBjb2xvciA9PT0gJ3llbGxvdycgPyAnI0ZGRkYwMCcgOiBjb2xvciA9PT0gJ2JsdWUnID8gJyMwMDAwRkYnIDogY29sb3IgPT09ICdibGFjaycgPyAnIzAwMDAwMCcgOiAnI0ZGRkZGRicpIDogJ3RyYW5zcGFyZW50JyxcbiAgICBib3JkZXJSaWdodENvbG9yOiBkaXJlY3Rpb24gPT09ICdsZWZ0JyA/IChjb2xvciA9PT0gJ3JlZCcgPyAnI0ZGMDAwMCcgOiBjb2xvciA9PT0gJ3llbGxvdycgPyAnI0ZGRkYwMCcgOiBjb2xvciA9PT0gJ2JsdWUnID8gJyMwMDAwRkYnIDogY29sb3IgPT09ICdibGFjaycgPyAnIzAwMDAwMCcgOiAnI0ZGRkZGRicpIDogJ3RyYW5zcGFyZW50J1xuICB9XG4gIFxuICByZXR1cm4gKFxuICAgIDxkaXYgXG4gICAgICBjbGFzc05hbWU9e2Ake3RyaWFuZ2xlQ2xhc3Nlc30gJHtjbGFzc05hbWV9YH1cbiAgICAgIHN0eWxlPXtzdHlsZX1cbiAgICAvPlxuICApXG59XG4iXSwibmFtZXMiOlsic2l6ZUNsYXNzZXMiLCJzbSIsIm1kIiwibGciLCJ4bCIsImdldFRyaWFuZ2xlQ2xhc3NlcyIsInNpemUiLCJjb2xvciIsImRpcmVjdGlvbiIsImNvbG9yTWFwIiwicmVkIiwieWVsbG93IiwiYmx1ZSIsImJsYWNrIiwid2hpdGUiLCJiYXNlU2l6ZSIsInRyaWFuZ2xlQ29sb3IiLCJyZXBsYWNlIiwiVHJpYW5nbGUiLCJjbGFzc05hbWUiLCJ0cmlhbmdsZUNsYXNzZXMiLCJzdHlsZSIsImJvcmRlckJvdHRvbUNvbG9yIiwiYm9yZGVyVG9wQ29sb3IiLCJib3JkZXJMZWZ0Q29sb3IiLCJib3JkZXJSaWdodENvbG9yIiwiZGl2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shapes/Triangle.tsx\n"));

/***/ })

});