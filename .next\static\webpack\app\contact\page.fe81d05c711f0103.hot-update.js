"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./src/components/shapes/Rectangle.tsx":
/*!*********************************************!*\
  !*** ./src/components/shapes/Rectangle.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Rectangle; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n\nconst widthClasses = {\n    sm: \"w-12\",\n    md: \"w-24\",\n    lg: \"w-32\",\n    xl: \"w-48\"\n};\nconst heightClasses = {\n    sm: \"h-8\",\n    md: \"h-16\",\n    lg: \"h-24\",\n    xl: \"h-32\"\n};\nconst colorClasses = {\n    red: \"bg-bauhaus-red\",\n    yellow: \"bg-bauhaus-yellow\",\n    blue: \"bg-bauhaus-blue\",\n    black: \"bg-bauhaus-black\",\n    white: \"bg-white border border-bauhaus-black\"\n};\nfunction Rectangle(param) {\n    let { width = \"md\", height = \"md\", color = \"blue\", className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(widthClasses[width], \" \").concat(heightClasses[height], \" \").concat(colorClasses[color], \" \").concat(className)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Projects\\\\navhaus\\\\src\\\\components\\\\shapes\\\\Rectangle.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_c = Rectangle;\nvar _c;\n$RefreshReg$(_c, \"Rectangle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shapes/Rectangle.tsx\n"));

/***/ })

});