# Navhaus - Bauhaus-Inspired Design Agency Website

A modern, clean, Bauhaus-inspired marketing website for Navhaus, a digital design and development agency. Built with Next.js, TypeScript, and Tailwind CSS.

## 🎨 Design Philosophy

This website embodies Bauhaus design principles:
- **Bold primary colors**: Red (#FF0000), Yellow (#FFFF00), Blue (#0000FF), Black, and White
- **Geometric shapes**: Circles, rectangles, triangles, and half-circles as visual elements
- **Clean typography**: Jost font from Google Fonts throughout
- **Minimal UI**: No shadows, gradients, or glassmorphism - just clean, flat design
- **Generous whitespace**: Spacious layouts that breathe
- **Sharp visual contrast**: Strong color blocks and clear hierarchy

## 🚀 Getting Started

### Prerequisites

Make sure you have Node.js installed on your system.

### Installation

1. **Add your logo**: Place your `logo.png` file in the `public/images/` directory

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── about/             # About page
│   ├── contact/           # Contact page  
│   ├── work/              # Work/portfolio page
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/
│   ├── layout/            # Layout components
│   │   ├── Header.tsx     # Navigation header
│   │   ├── Footer.tsx     # Site footer
│   │   └── PageWrapper.tsx # Page wrapper
│   └── shapes/            # Geometric shape components
│       ├── Circle.tsx     # Circle component
│       ├── Rectangle.tsx  # Rectangle component
│       ├── Triangle.tsx   # Triangle component
│       └── HalfCircle.tsx # Half-circle component
```

## 🎯 Features

- **Responsive Design**: Works perfectly on mobile, tablet, and desktop
- **Bauhaus Aesthetics**: Authentic geometric compositions and color palette
- **Modern Stack**: Next.js 14 with TypeScript and Tailwind CSS
- **Clean Code**: Well-organized components and consistent styling
- **Fast Performance**: Optimized for speed and SEO
- **Accessible**: Semantic HTML and proper contrast ratios

## 🎨 Color Palette

- **Red**: #e94436 (Primary accent)
- **Yellow**: #ffc527 (Secondary accent)
- **Blue**: #434897 (Tertiary accent)
- **Black**: #000000 (Text and borders)
- **Background**: #f0ebde (Warm cream background)

## 📱 Pages

1. **Home**: Hero section with geometric composition and feature highlights
2. **About**: Company introduction with belief cards and decorative elements
3. **Work**: Project portfolio with geometric thumbnails and case studies
4. **Contact**: Clean contact form with abstract shape collage

## 🛠 Built With

- [Next.js 14](https://nextjs.org/) - React framework
- [TypeScript](https://www.typescriptlang.org/) - Type safety
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS
- [Jost Font](https://fonts.google.com/specimen/Jost) - Typography

## 📄 License

This project is for demonstration purposes. All rights reserved.
