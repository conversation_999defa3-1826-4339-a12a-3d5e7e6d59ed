import PageWrapper from '@/components/layout/PageWrapper'
import Circle from '@/components/shapes/Circle'
import Rectangle from '@/components/shapes/Rectangle'
import Triangle from '@/components/shapes/Triangle'
import HalfCircle from '@/components/shapes/HalfCircle'
import OrganicComposition from '@/components/compositions/OrganicComposition'
import { SoftCircle } from '@/components/shapes/Circle'
import { RoundedRectangle } from '@/components/shapes/Rectangle'
import Link from 'next/link'

export default function Home() {
  return (
    <PageWrapper>
      {/* Hero Section */}
      <section className="relative px-6 md:px-12 lg:px-24 py-16 md:py-24 lg:py-32 overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            {/* Hero Content */}
            <div className="space-y-8">
              <h1 className="text-hero font-bold leading-tight">
                What matters, made real.
              </h1>
              <p className="text-xl md:text-2xl leading-relaxed text-gray-700 max-w-lg">
                Navhaus is a design and development studio that builds bold, efficient, 
                and meaningful digital experiences — nothing more, nothing less.
              </p>
              <Link href="/contact" className="btn-red inline-block">
                Start Your Project
              </Link>
            </div>
            
            {/* Organic Geometric Composition */}
            <div className="relative h-96 lg:h-[500px]">
              <OrganicComposition variant="hero" className="w-full h-full" />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="px-6 md:px-12 lg:px-24 py-16 md:py-24">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12 md:gap-8 lg:gap-12">
            {/* Feature 1 */}
            <div className="text-center space-y-6">
              <div className="flex justify-center">
                <SoftCircle size="xl" color="red" />
              </div>
              <h3 className="text-heading font-bold">Fast Builds</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                We get things out the door fast without breaking things later.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="text-center space-y-6">
              <div className="flex justify-center">
                <RoundedRectangle width="xl" height="lg" color="blue" />
              </div>
              <h3 className="text-heading font-bold">Design-Driven</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                We don't chase trends. We build with clarity and purpose.
              </p>
            </div>

            {/* Feature 3 */}
            <div className="text-center space-y-6">
              <div className="flex justify-center">
                <Triangle size="xl" color="yellow" direction="up" />
              </div>
              <h3 className="text-heading font-bold">No Bloat</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                You'll only get what matters. No filler, no noise, no wasted code.
              </p>
            </div>
          </div>
        </div>
      </section>
    </PageWrapper>
  )
}
