import PageWrapper from '@/components/layout/PageWrapper'
import Circle from '@/components/shapes/Circle'
import Rectangle from '@/components/shapes/Rectangle'
import Triangle from '@/components/shapes/Triangle'
import HalfCircle from '@/components/shapes/HalfCircle'
import OrganicComposition from '@/components/compositions/OrganicComposition'
import { SoftCircle } from '@/components/shapes/Circle'
import { RoundedRectangle } from '@/components/shapes/Rectangle'
import { SoftGrid } from '@/components/shapes/RoundedShapes'
import Link from 'next/link'

export default function Home() {
  return (
    <PageWrapper>
      {/* Hero Section */}
      <section className="relative px-6 md:px-12 lg:px-24 py-16 md:py-24 lg:py-32 overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            {/* Hero Content */}
            <div className="space-y-8">
              <h1 className="text-hero font-bold leading-tight">
                What matters, made real.
              </h1>
              <p className="text-xl md:text-2xl leading-relaxed text-gray-700 max-w-lg">
                Navhaus is a design and development studio that builds bold, efficient, 
                and meaningful digital experiences — nothing more, nothing less.
              </p>
              <Link href="/contact" className="btn-red inline-block">
                Start Your Project
              </Link>
            </div>
            
            {/* Organic Geometric Composition */}
            <div className="relative h-96 lg:h-[500px]">
              <OrganicComposition variant="hero" className="w-full h-full" />
            </div>
          </div>
        </div>
      </section>

      {/* How We Work Section */}
      <section className="px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-display font-bold text-center mb-20">How We Work</h2>

          <div className="space-y-24">
            {/* Process 1 - Left aligned */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="relative h-64 w-full">
                {/* Grid background */}
                <div className="absolute inset-0">
                  <SoftGrid className="w-full h-full text-black" />
                </div>
                {/* Main shape */}
                <div className="relative flex justify-center lg:justify-start items-center h-full">
                  <SoftCircle size="xl" color="blue" className="w-32 h-32" />
                </div>
                {/* Dense decorative elements */}
                <div className="absolute top-4 right-8 opacity-80">
                  <RoundedRectangle width="md" height="sm" color="yellow" className="w-12 h-8" />
                </div>
                <div className="absolute bottom-8 left-16 opacity-80">
                  <SoftCircle size="md" color="red" className="w-10 h-10" />
                </div>
                <div className="absolute top-1/2 right-4 opacity-70">
                  <Triangle size="md" color="red" direction="up" className="w-8 h-8" />
                </div>
                <div className="absolute top-8 left-8 opacity-60">
                  <RoundedRectangle width="sm" height="xs" color="red" className="w-6 h-3" />
                </div>
                <div className="absolute bottom-4 right-16 opacity-70">
                  <SoftCircle size="sm" color="yellow" className="w-6 h-6" />
                </div>
                <div className="absolute top-16 right-20 opacity-50">
                  <Triangle size="xs" color="blue" direction="up" className="w-4 h-4" />
                </div>
                <div className="absolute bottom-16 left-4 opacity-60">
                  <RoundedRectangle width="xs" height="xs" color="blue" className="w-4 h-4" />
                </div>
                <div className="absolute top-1/3 left-1/3 opacity-50">
                  <SoftCircle size="xs" color="yellow" className="w-4 h-4" />
                </div>
              </div>
              <div className="space-y-6 text-center lg:text-left">
                <h3 className="text-heading font-bold">Listen</h3>
                <p className="text-body text-gray-700 leading-relaxed text-lg">
                  We start by understanding exactly what you're trying to do — and more importantly, why.
                </p>
              </div>
            </div>

            {/* Process 2 - Right aligned */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-6 text-center lg:text-right lg:order-1">
                <h3 className="text-heading font-bold">Design</h3>
                <p className="text-body text-gray-700 leading-relaxed text-lg">
                  We strip away noise, leaving only what needs to be there. Everything has a reason.
                </p>
              </div>
              <div className="relative h-64 w-full lg:order-2">
                {/* Grid background */}
                <div className="absolute inset-0">
                  <SoftGrid className="w-full h-full text-black" />
                </div>
                {/* Main shape */}
                <div className="relative flex justify-center lg:justify-end items-center h-full">
                  <RoundedRectangle width="2xl" height="xl" color="yellow" className="w-32 h-24" />
                </div>
                {/* Dense decorative elements */}
                <div className="absolute top-6 left-8 opacity-80">
                  <SoftCircle size="lg" color="blue" className="w-14 h-14" />
                </div>
                <div className="absolute bottom-4 right-20 opacity-80">
                  <Triangle size="md" color="red" direction="up" className="w-8 h-8" />
                </div>
                <div className="absolute top-1/3 left-4 opacity-70">
                  <RoundedRectangle width="sm" height="sm" color="blue" className="w-6 h-6" />
                </div>
                <div className="absolute bottom-1/3 left-1/3 opacity-70">
                  <SoftCircle size="sm" color="red" className="w-6 h-6" />
                </div>
                <div className="absolute top-4 right-4 opacity-60">
                  <Triangle size="sm" color="blue" direction="up" className="w-5 h-5" />
                </div>
                <div className="absolute bottom-8 left-20 opacity-60">
                  <RoundedRectangle width="xs" height="md" color="red" className="w-3 h-8" />
                </div>
                <div className="absolute top-20 left-16 opacity-50">
                  <SoftCircle size="xs" color="yellow" className="w-4 h-4" />
                </div>
                <div className="absolute bottom-20 right-8 opacity-60">
                  <Triangle size="xs" color="yellow" direction="up" className="w-3 h-3" />
                </div>
                <div className="absolute top-1/2 left-1/2 opacity-50">
                  <RoundedRectangle width="xs" height="xs" color="blue" className="w-3 h-3" />
                </div>
              </div>
            </div>

            {/* Process 3 - Left aligned */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="relative h-64 w-full">
                {/* Grid background */}
                <div className="absolute inset-0">
                  <SoftGrid className="w-full h-full text-black" />
                </div>
                {/* Building blocks composition */}
                <div className="relative flex justify-center lg:justify-start items-center h-full">
                  {/* Base foundation - larger and more prominent */}
                  <div className="absolute bottom-12">
                    <RoundedRectangle width="2xl" height="lg" color="red" className="w-40 h-12" />
                  </div>
                  {/* Middle layer */}
                  <div className="absolute bottom-20 left-4">
                    <RoundedRectangle width="xl" height="lg" color="blue" className="w-32 h-12" />
                  </div>
                  {/* Top layer */}
                  <div className="absolute bottom-28 left-8">
                    <RoundedRectangle width="lg" height="lg" color="yellow" className="w-24 h-12" />
                  </div>
                  {/* Connecting elements - larger and more visible */}
                  <div className="absolute bottom-16 right-4 opacity-80">
                    <SoftCircle size="md" color="yellow" className="w-10 h-10" />
                  </div>
                  <div className="absolute bottom-24 right-8 opacity-80">
                    <SoftCircle size="sm" color="red" className="w-6 h-6" />
                  </div>
                  <div className="absolute bottom-32 right-12 opacity-70">
                    <Triangle size="sm" color="blue" direction="up" className="w-6 h-6" />
                  </div>
                  {/* Dense decorative elements */}
                  <div className="absolute top-8 right-4 opacity-70">
                    <Triangle size="md" color="blue" direction="up" className="w-8 h-8" />
                  </div>
                  <div className="absolute top-16 left-4 opacity-60">
                    <RoundedRectangle width="sm" height="sm" color="yellow" className="w-6 h-6" />
                  </div>
                  <div className="absolute top-4 left-12 opacity-60">
                    <SoftCircle size="sm" color="red" className="w-6 h-6" />
                  </div>
                  <div className="absolute top-12 right-16 opacity-50">
                    <RoundedRectangle width="xs" height="md" color="red" className="w-3 h-6" />
                  </div>
                  <div className="absolute top-20 left-16 opacity-50">
                    <Triangle size="xs" color="yellow" direction="up" className="w-4 h-4" />
                  </div>
                  <div className="absolute top-6 left-20 opacity-40">
                    <SoftCircle size="xs" color="blue" className="w-4 h-4" />
                  </div>
                  <div className="absolute bottom-8 left-20 opacity-60">
                    <RoundedRectangle width="xs" height="xs" color="yellow" className="w-4 h-4" />
                  </div>
                </div>
              </div>
              <div className="space-y-6 text-center lg:text-left">
                <h3 className="text-heading font-bold">Build</h3>
                <p className="text-body text-gray-700 leading-relaxed text-lg">
                  We ship clean code and scalable systems. Fast. Reliable. Yours to own.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Things We Love Building Section */}
      <section className="relative px-6 md:px-12 lg:px-24 py-16 md:py-24 overflow-hidden">
        <div className="max-w-7xl mx-auto relative z-10">
          <h2 className="text-display font-bold text-center mb-8">Things We Love Building</h2>
          <p className="text-xl text-center text-gray-700 mb-16 max-w-3xl mx-auto">
            No big case studies (yet), but here's what gets us fired up.
          </p>

          {/* Decorative shapes with grids */}
          <div className="absolute top-0 left-0 opacity-60">
            <div className="relative">
              <SoftGrid className="w-24 h-24 text-black" />
              <SoftCircle size="lg" color="red" className="absolute top-2 left-2 w-16 h-16" />
            </div>
          </div>
          <div className="absolute top-1/2 right-0 opacity-60">
            <div className="relative">
              <SoftGrid className="w-32 h-32 text-black" />
              <RoundedRectangle width="lg" height="md" color="blue" className="absolute top-4 left-4 w-20 h-12" />
            </div>
          </div>
          <div className="absolute bottom-0 left-1/3 opacity-60">
            <div className="relative">
              <SoftGrid className="w-28 h-28 text-black" />
              <Triangle size="lg" color="yellow" direction="up" className="absolute top-3 left-3 w-18 h-18" />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
            {/* Service 1 */}
            <div className="bg-brand-background border-2 border-bauhaus-black rounded-2xl p-8 space-y-4">
              <h3 className="text-heading font-bold">High-performance marketing sites</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                Built to load fast, convert hard, and stay lean.
              </p>
            </div>

            {/* Service 2 */}
            <div className="bg-brand-background border-2 border-bauhaus-black rounded-2xl p-8 space-y-4">
              <h3 className="text-heading font-bold">Design systems that scale</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                Interfaces that grow without turning to chaos.
              </p>
            </div>

            {/* Service 3 */}
            <div className="bg-brand-background border-2 border-bauhaus-black rounded-2xl p-8 space-y-4">
              <h3 className="text-heading font-bold">Custom Gutenberg blocks</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                Purpose-built. No extra plugins. Just exactly what you need.
              </p>
            </div>

            {/* Service 4 */}
            <div className="bg-brand-background border-2 border-bauhaus-black rounded-2xl p-8 space-y-4">
              <h3 className="text-heading font-bold">API-connected web apps</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                Lightweight, maintainable, and fast as hell.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Who We're For Section */}
      <section className="px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-brand-background">
        <div className="max-w-5xl mx-auto text-center">
          <h2 className="text-display font-bold mb-8">Who We're For</h2>
          <p className="text-xl text-gray-700 leading-relaxed mb-12">
            We work with founders, marketers, and teams who value clarity over chaos.<br />
            People who want:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-left max-w-3xl mx-auto mb-12">
            <div className="space-y-4">
              <p className="text-body text-gray-700">• Smaller teams, fewer meetings</p>
              <p className="text-body text-gray-700">• Transparent communication</p>
            </div>
            <div className="space-y-4">
              <p className="text-body text-gray-700">• Clean design, clean code</p>
              <p className="text-body text-gray-700">• Something real, built fast</p>
            </div>
          </div>
          <p className="text-xl font-bold text-gray-900">
            Still new here? That just means we're hungrier to prove it.
          </p>
        </div>
      </section>

      {/* What We Build With Section */}
      <section className="px-6 md:px-12 lg:px-24 py-16 md:py-24">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-display font-bold text-center mb-8">What We Build With</h2>
          <p className="text-xl text-center text-gray-700 mb-16 max-w-4xl mx-auto">
            We use tools that are fast, modern, and built to scale. Everything is chosen for clarity, performance, and long-term maintainability — no fluff, no filler.
          </p>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Core Stack */}
            <div className="space-y-8">
              <h3 className="text-heading font-bold">Core Stack</h3>
              <div className="space-y-6">
                <div>
                  <h4 className="font-bold text-lg mb-2">WordPress (Custom Themes)</h4>
                  <p className="text-gray-700">Built from scratch — clean, minimal, and made to last.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Sage (Roots stack)</h4>
                  <p className="text-gray-700">A modern workflow for WordPress, using Blade, Composer, and clean structure.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Blade</h4>
                  <p className="text-gray-700">Elegant templating with clear separation between logic and layout.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Gutenberg (Custom Blocks)</h4>
                  <p className="text-gray-700">Native block development with PHP and JavaScript — no ACF Pro.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">PHP</h4>
                  <p className="text-gray-700">Lightweight and structured backend logic.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">JavaScript (Vanilla)</h4>
                  <p className="text-gray-700">Used for interactions and custom block logic — nothing unnecessary.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Tailwind CSS</h4>
                  <p className="text-gray-700">Utility-first styling that keeps everything lean and consistent.</p>
                </div>
              </div>
            </div>

            {/* Modern Frontend */}
            <div className="space-y-8">
              <h3 className="text-heading font-bold">Modern Frontend</h3>
              <div className="space-y-6">
                <div>
                  <h4 className="font-bold text-lg mb-2">React (when needed)</h4>
                  <p className="text-gray-700">For rich UIs, headless builds, or interactive components — used with intent.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Headless WordPress</h4>
                  <p className="text-gray-700">REST-based content delivery with frameworks like Next.js or custom React frontends.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Alpine.js</h4>
                  <p className="text-gray-700">When reactivity's needed but React would be overkill.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Vite + ESBuild</h4>
                  <p className="text-gray-700">Fast bundling, instant reloads, and smooth local development.</p>
                </div>
              </div>

              <h3 className="text-heading font-bold pt-8">Tooling & Workflow</h3>
              <div className="space-y-6">
                <div>
                  <h4 className="font-bold text-lg mb-2">Composer / NPM / Yarn</h4>
                  <p className="text-gray-700">Proper package management — backend and frontend.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">PostCSS</h4>
                  <p className="text-gray-700">Tailwind config, purging, prefixing, and build tweaks.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Git + GitHub</h4>
                  <p className="text-gray-700">Everything versioned, reviewed, and documented.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Figma</h4>
                  <p className="text-gray-700">Used for design systems, wireframes, and handoff.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Markdown + Docs</h4>
                  <p className="text-gray-700">Clean documentation for devs and clients.</p>
                </div>
              </div>
            </div>

            {/* Extended Capabilities */}
            <div className="space-y-8">
              <h3 className="text-heading font-bold">Extended Capabilities</h3>
              <div className="space-y-6">
                <div>
                  <h4 className="font-bold text-lg mb-2">Next.js</h4>
                  <p className="text-gray-700">Modern React frontend when needed</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">MySQL</h4>
                  <p className="text-gray-700">Standard WordPress data layer</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">DigitalOcean / Vercel / Netlify</h4>
                  <p className="text-gray-700">Deployment options tailored to the stack</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">REST APIs</h4>
                  <p className="text-gray-700">For custom integrations, data, or decoupled setups</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="px-6 md:px-12 lg:px-24 py-24 md:py-32 bg-bauhaus-black text-bauhaus-white relative overflow-hidden">
        <div className="max-w-4xl mx-auto text-center relative z-10">
          <h2 className="text-display font-bold mb-6">Got something worth building?</h2>
          <p className="text-xl mb-12 leading-relaxed">
            Let's make it real. We'll help you strip it down to what matters — and bring it to life.
          </p>
          <Link href="/contact" className="btn-primary bg-brand-background text-bauhaus-black border-brand-background hover:bg-transparent hover:text-brand-background inline-block">
            Start Your Project
          </Link>
        </div>

        {/* Geometric motif with grids */}
        <div className="absolute inset-0 opacity-10">
          <OrganicComposition variant="feature" className="w-full h-full" />
        </div>
        <div className="absolute top-8 left-8 opacity-60">
          <div className="relative">
            <SoftGrid className="w-32 h-32 text-black" />
            <SoftCircle size="xl" color="red" className="absolute top-4 left-4 w-20 h-20" />
          </div>
        </div>
        <div className="absolute bottom-8 right-8 opacity-60">
          <div className="relative">
            <SoftGrid className="w-28 h-28 text-black" />
            <RoundedRectangle width="xl" height="lg" color="yellow" className="absolute top-3 left-3 w-18 h-14" />
          </div>
        </div>
      </section>
    </PageWrapper>
  )
}
