import PageWrapper from '@/components/layout/PageWrapper'
import Circle from '@/components/shapes/Circle'
import Rectangle from '@/components/shapes/Rectangle'
import Triangle from '@/components/shapes/Triangle'
import HalfCircle from '@/components/shapes/HalfCircle'
import OrganicComposition from '@/components/compositions/OrganicComposition'
import { SoftCircle } from '@/components/shapes/Circle'
import { RoundedRectangle } from '@/components/shapes/Rectangle'
import Link from 'next/link'

export default function Home() {
  return (
    <PageWrapper>
      {/* Hero Section */}
      <section className="relative px-6 md:px-12 lg:px-24 py-16 md:py-24 lg:py-32 overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            {/* Hero Content */}
            <div className="space-y-8">
              <h1 className="text-hero font-bold leading-tight">
                What matters, made real.
              </h1>
              <p className="text-xl md:text-2xl leading-relaxed text-gray-700 max-w-lg">
                Navhaus is a design and development studio that builds bold, efficient, 
                and meaningful digital experiences — nothing more, nothing less.
              </p>
              <Link href="/contact" className="btn-red inline-block">
                Start Your Project
              </Link>
            </div>
            
            {/* Organic Geometric Composition */}
            <div className="relative h-96 lg:h-[500px]">
              <OrganicComposition variant="hero" className="w-full h-full" />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="px-6 md:px-12 lg:px-24 py-16 md:py-24">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-display font-bold text-center mb-16">No Fluff. Just the Good Stuff.</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12 md:gap-8 lg:gap-12">
            {/* Feature 1 */}
            <div className="text-center space-y-6">
              <div className="flex justify-center">
                <SoftCircle size="xl" color="red" />
              </div>
              <h3 className="text-heading font-bold">Fast Builds</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                We get things out the door fast without breaking things later.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="text-center space-y-6">
              <div className="flex justify-center">
                <RoundedRectangle width="xl" height="lg" color="blue" />
              </div>
              <h3 className="text-heading font-bold">Design-Driven</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                We don't chase trends. We build with clarity and purpose.
              </p>
            </div>

            {/* Feature 3 */}
            <div className="text-center space-y-6">
              <div className="flex justify-center">
                <Triangle size="xl" color="yellow" direction="up" />
              </div>
              <h3 className="text-heading font-bold">No Bloat</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                You'll only get what matters. No filler, no noise, no wasted code.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-display font-bold text-center mb-16">How We Work</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12 md:gap-8 lg:gap-12">
            {/* Process 1 */}
            <div className="text-center space-y-6">
              <div className="flex justify-center">
                <SoftCircle size="xl" color="blue" />
              </div>
              <h3 className="text-heading font-bold">Listen</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                We start by understanding exactly what you're trying to do — and more importantly, why.
              </p>
            </div>

            {/* Process 2 */}
            <div className="text-center space-y-6">
              <div className="flex justify-center">
                <RoundedRectangle width="xl" height="lg" color="yellow" />
              </div>
              <h3 className="text-heading font-bold">Design</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                We strip away noise, leaving only what needs to be there. Everything has a reason.
              </p>
            </div>

            {/* Process 3 */}
            <div className="text-center space-y-6">
              <div className="flex justify-center">
                <Triangle size="xl" color="red" direction="up" />
              </div>
              <h3 className="text-heading font-bold">Build</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                We ship clean code and scalable systems. Fast. Reliable. Yours to own.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Things We Love Building Section */}
      <section className="px-6 md:px-12 lg:px-24 py-16 md:py-24">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-display font-bold text-center mb-8">Things We Love Building</h2>
          <p className="text-xl text-center text-gray-700 mb-16 max-w-3xl mx-auto">
            No big case studies (yet), but here's what gets us fired up.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
            {/* Service 1 */}
            <div className="bg-bauhaus-white border-2 border-bauhaus-black rounded-2xl p-8 space-y-4">
              <h3 className="text-heading font-bold">High-performance marketing sites</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                Built to load fast, convert hard, and stay lean.
              </p>
            </div>

            {/* Service 2 */}
            <div className="bg-bauhaus-white border-2 border-bauhaus-black rounded-2xl p-8 space-y-4">
              <h3 className="text-heading font-bold">Design systems that scale</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                Interfaces that grow without turning to chaos.
              </p>
            </div>

            {/* Service 3 */}
            <div className="bg-bauhaus-white border-2 border-bauhaus-black rounded-2xl p-8 space-y-4">
              <h3 className="text-heading font-bold">Custom Gutenberg blocks</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                Purpose-built. No extra plugins. Just exactly what you need.
              </p>
            </div>

            {/* Service 4 */}
            <div className="bg-bauhaus-white border-2 border-bauhaus-black rounded-2xl p-8 space-y-4">
              <h3 className="text-heading font-bold">API-connected web apps</h3>
              <p className="text-body text-gray-700 leading-relaxed">
                Lightweight, maintainable, and fast as hell.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Who We're For Section */}
      <section className="px-6 md:px-12 lg:px-24 py-16 md:py-24 bg-gray-50">
        <div className="max-w-5xl mx-auto text-center">
          <h2 className="text-display font-bold mb-8">Who We're For</h2>
          <p className="text-xl text-gray-700 leading-relaxed mb-12">
            We work with founders, marketers, and teams who value clarity over chaos.<br />
            People who want:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-left max-w-3xl mx-auto mb-12">
            <div className="space-y-4">
              <p className="text-body text-gray-700">• Smaller teams, fewer meetings</p>
              <p className="text-body text-gray-700">• Transparent communication</p>
            </div>
            <div className="space-y-4">
              <p className="text-body text-gray-700">• Clean design, clean code</p>
              <p className="text-body text-gray-700">• Something real, built fast</p>
            </div>
          </div>
          <p className="text-xl font-bold text-gray-900">
            Still new here? That just means we're hungrier to prove it.
          </p>
        </div>
      </section>

      {/* What We Build With Section */}
      <section className="px-6 md:px-12 lg:px-24 py-16 md:py-24">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-display font-bold text-center mb-8">What We Build With</h2>
          <p className="text-xl text-center text-gray-700 mb-16 max-w-4xl mx-auto">
            We use tools that are fast, modern, and built to scale. Everything is chosen for clarity, performance, and long-term maintainability — no fluff, no filler.
          </p>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Core Stack */}
            <div className="space-y-8">
              <h3 className="text-heading font-bold">Core Stack</h3>
              <div className="space-y-6">
                <div>
                  <h4 className="font-bold text-lg mb-2">WordPress (Custom Themes)</h4>
                  <p className="text-gray-700">Built from scratch — clean, minimal, and made to last.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Sage (Roots stack)</h4>
                  <p className="text-gray-700">A modern workflow for WordPress, using Blade, Composer, and clean structure.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Blade</h4>
                  <p className="text-gray-700">Elegant templating with clear separation between logic and layout.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Gutenberg (Custom Blocks)</h4>
                  <p className="text-gray-700">Native block development with PHP and JavaScript — no ACF Pro.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">PHP</h4>
                  <p className="text-gray-700">Lightweight and structured backend logic.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">JavaScript (Vanilla)</h4>
                  <p className="text-gray-700">Used for interactions and custom block logic — nothing unnecessary.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Tailwind CSS</h4>
                  <p className="text-gray-700">Utility-first styling that keeps everything lean and consistent.</p>
                </div>
              </div>
            </div>

            {/* Modern Frontend */}
            <div className="space-y-8">
              <h3 className="text-heading font-bold">Modern Frontend</h3>
              <div className="space-y-6">
                <div>
                  <h4 className="font-bold text-lg mb-2">React (when needed)</h4>
                  <p className="text-gray-700">For rich UIs, headless builds, or interactive components — used with intent.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Headless WordPress</h4>
                  <p className="text-gray-700">REST-based content delivery with frameworks like Next.js or custom React frontends.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Alpine.js</h4>
                  <p className="text-gray-700">When reactivity's needed but React would be overkill.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Vite + ESBuild</h4>
                  <p className="text-gray-700">Fast bundling, instant reloads, and smooth local development.</p>
                </div>
              </div>

              <h3 className="text-heading font-bold pt-8">Tooling & Workflow</h3>
              <div className="space-y-6">
                <div>
                  <h4 className="font-bold text-lg mb-2">Composer / NPM / Yarn</h4>
                  <p className="text-gray-700">Proper package management — backend and frontend.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">PostCSS</h4>
                  <p className="text-gray-700">Tailwind config, purging, prefixing, and build tweaks.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Git + GitHub</h4>
                  <p className="text-gray-700">Everything versioned, reviewed, and documented.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Figma</h4>
                  <p className="text-gray-700">Used for design systems, wireframes, and handoff.</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">Markdown + Docs</h4>
                  <p className="text-gray-700">Clean documentation for devs and clients.</p>
                </div>
              </div>
            </div>

            {/* Extended Capabilities */}
            <div className="space-y-8">
              <h3 className="text-heading font-bold">Extended Capabilities</h3>
              <div className="space-y-6">
                <div>
                  <h4 className="font-bold text-lg mb-2">Next.js</h4>
                  <p className="text-gray-700">Modern React frontend when needed</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">MySQL</h4>
                  <p className="text-gray-700">Standard WordPress data layer</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">DigitalOcean / Vercel / Netlify</h4>
                  <p className="text-gray-700">Deployment options tailored to the stack</p>
                </div>
                <div>
                  <h4 className="font-bold text-lg mb-2">REST APIs</h4>
                  <p className="text-gray-700">For custom integrations, data, or decoupled setups</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="px-6 md:px-12 lg:px-24 py-24 md:py-32 bg-bauhaus-black text-bauhaus-white relative overflow-hidden">
        <div className="max-w-4xl mx-auto text-center relative z-10">
          <h2 className="text-display font-bold mb-6">Got something worth building?</h2>
          <p className="text-xl mb-12 leading-relaxed">
            Let's make it real. We'll help you strip it down to what matters — and bring it to life.
          </p>
          <Link href="/contact" className="btn-primary bg-bauhaus-white text-bauhaus-black border-bauhaus-white hover:bg-transparent hover:text-bauhaus-white inline-block">
            Start Your Project
          </Link>
        </div>

        {/* Geometric motif */}
        <div className="absolute inset-0 flex items-center justify-center opacity-10">
          <OrganicComposition variant="feature" className="w-full h-full" />
        </div>
      </section>
    </PageWrapper>
  )
}
