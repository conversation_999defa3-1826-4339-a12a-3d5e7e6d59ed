interface CircleProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'red' | 'yellow' | 'blue' | 'black' | 'white'
  className?: string
}

const sizeClasses = {
  sm: 'w-8 h-8',
  md: 'w-16 h-16', 
  lg: 'w-24 h-24',
  xl: 'w-32 h-32'
}

const colorClasses = {
  red: 'bg-bauhaus-red',
  yellow: 'bg-bauhaus-yellow',
  blue: 'bg-bauhaus-blue',
  black: 'bg-bauhaus-black',
  white: 'bg-white border border-bauhaus-black'
}

export default function Circle({ 
  size = 'md', 
  color = 'red', 
  className = '' 
}: CircleProps) {
  return (
    <div 
      className={`rounded-full ${sizeClasses[size]} ${colorClasses[color]} ${className}`}
    />
  )
}
