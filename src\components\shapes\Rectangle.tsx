interface RectangleProps {
  width?: 'sm' | 'md' | 'lg' | 'xl'
  height?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'red' | 'yellow' | 'blue' | 'black' | 'white'
  className?: string
}

const widthClasses = {
  sm: 'w-12',
  md: 'w-24',
  lg: 'w-32', 
  xl: 'w-48'
}

const heightClasses = {
  sm: 'h-8',
  md: 'h-16',
  lg: 'h-24',
  xl: 'h-32'
}

const colorClasses = {
  red: 'bg-bauhaus-red',
  yellow: 'bg-bauhaus-yellow',
  blue: 'bg-bauhaus-blue',
  black: 'bg-bauhaus-black',
  white: 'bg-white border border-bauhaus-black'
}

export default function Rectangle({ 
  width = 'md',
  height = 'md', 
  color = 'blue',
  className = '' 
}: RectangleProps) {
  return (
    <div 
      className={`${widthClasses[width]} ${heightClasses[height]} ${colorClasses[color]} ${className}`}
    />
  )
}
