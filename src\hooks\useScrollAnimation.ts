'use client'

import { useEffect, useState, useRef } from 'react'

interface ScrollAnimationConfig {
  direction: 'x' | 'y' | 'both'
  intensity: number
  speed: number
  randomSeed?: number
}

export function useScrollAnimation(config: ScrollAnimationConfig) {
  const [scrollY, setScrollY] = useState(0)
  const elementRef = useRef<HTMLDivElement>(null)
  const [randomFactors, setRandomFactors] = useState({ x: 0, y: 0 })

  // Generate consistent random factors based on seed
  useEffect(() => {
    const seed = config.randomSeed || Math.random()
    // Use seed to generate consistent random values between -1 and 1
    const x = (Math.sin(seed * 12.9898) * 43758.5453) % 1
    const y = (Math.sin(seed * 78.233) * 43758.5453) % 1
    
    setRandomFactors({
      x: (x - 0.5) * 2, // Convert to -1 to 1 range
      y: (y - 0.5) * 2
    })
  }, [config.randomSeed])

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY)
    }

    // Use passive listener for better performance
    window.addEventListener('scroll', handleScroll, { passive: true })
    
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  // Calculate transform values
  const getTransform = () => {
    if (!elementRef.current) return 'translate3d(0, 0, 0)'

    const rect = elementRef.current.getBoundingClientRect()
    const elementTop = rect.top + scrollY
    const scrollProgress = (scrollY - elementTop + window.innerHeight) / (window.innerHeight + rect.height)
    
    // Smooth easing function
    const easeInOutQuad = (t: number) => {
      return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t
    }
    
    const easedProgress = easeInOutQuad(Math.max(0, Math.min(1, scrollProgress)))
    
    let translateX = 0
    let translateY = 0
    
    if (config.direction === 'x' || config.direction === 'both') {
      translateX = easedProgress * config.intensity * randomFactors.x * config.speed
    }
    
    if (config.direction === 'y' || config.direction === 'both') {
      translateY = easedProgress * config.intensity * randomFactors.y * config.speed
    }
    
    return `translate3d(${translateX}px, ${translateY}px, 0)`
  }

  return {
    ref: elementRef,
    style: {
      transform: getTransform(),
      transition: 'transform 0.1s ease-out',
      willChange: 'transform'
    }
  }
}

// Predefined animation presets for different shape types
export const animationPresets = {
  subtle: {
    direction: 'both' as const,
    intensity: 20,
    speed: 0.5
  },
  gentle: {
    direction: 'both' as const,
    intensity: 30,
    speed: 0.7
  },
  dynamic: {
    direction: 'both' as const,
    intensity: 50,
    speed: 1
  },
  horizontal: {
    direction: 'x' as const,
    intensity: 40,
    speed: 0.8
  },
  vertical: {
    direction: 'y' as const,
    intensity: 35,
    speed: 0.6
  }
}
